# 🎓 UPF Smart Finance

**Plateforme de Gestion Financière Universitaire Moderne**

Une solution complète de gestion financière conçue spécialement pour l'Université de Polynésie Française, offrant une interface moderne et intuitive pour tous les acteurs du processus financier universitaire.

## 🌟 Aperçu du Projet

UPF Smart Finance révolutionne la gestion financière universitaire en centralisant tous les processus de paiement, de suivi et de reporting dans une plateforme web moderne et sécurisée.

### ✨ Points Forts
- 🔐 **Authentification sécurisée** avec gestion des rôles
- 💳 **Paiements en ligne** via Stripe (cartes bancaires et virements)
- 📊 **Tableaux de bord** personnalisés par rôle
- 📱 **Design responsive** pour tous les appareils
- 🔄 **Temps réel** pour le suivi des paiements
- 📈 **Rapports détaillés** et exports

## 🏗️ Architecture

```
UPF Smart Finance/
├── frontend/          # Application React.js
│   ├── src/
│   │   ├── components/    # Composants UI
│   │   ├── contexts/      # Gestion d'état
│   │   └── ...
│   └── package.json
└── README.md
```

## 🔐 Rôles et Permissions

### 👨‍🎓 Étudiant
- ✅ Consultation du solde
- ✅ Visualisation de l'échéancier
- ✅ Paiements en ligne sécurisés
- ✅ Téléchargement des reçus
- ✅ Gestion des relances

### 💼 Agent Financier
- ✅ Gestion des comptes étudiants
- ✅ Suivi des paiements en temps réel
- ✅ Génération de rapports
- ✅ Système de relances automatisé
- ✅ Export des données (CSV/PDF)

### 📚 Personnel Académique
- ✅ Consultation des données financières (lecture seule)
- ✅ Accès aux rapports de paiement
- ✅ Vue d'ensemble des étudiants

### ⚙️ Administrateur
- ✅ Gestion complète des utilisateurs
- ✅ Configuration système
- ✅ Logs d'activité détaillés
- ✅ Intégrations tierces (Stripe, Sage)

## 🚀 Démarrage Rapide

### Prérequis
- Node.js 16+ 
- npm ou yarn
- Navigateur moderne

### Installation

1. **Cloner le repository**
   ```bash
   git clone <repository-url>
   cd UPF
   ```

2. **Installer et lancer le frontend**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

3. **Accéder à l'application**
   Ouvrir `http://localhost:5173` dans votre navigateur

## 🧪 Comptes de Test

L'application inclut des comptes de démonstration prêts à utiliser :

| 👤 Rôle | 📧 Email | 🔑 Mot de passe |
|----------|----------|------------------|
| Étudiant | <EMAIL> | password123 |
| Agent Financier | <EMAIL> | password123 |
| Personnel Académique | <EMAIL> | password123 |
| Administrateur | <EMAIL> | password123 |

## 🛠️ Technologies

### Frontend
- **React 18** - Framework UI moderne
- **Vite** - Build tool ultra-rapide
- **React Router** - Navigation SPA
- **Lucide React** - Icônes élégantes
- **CSS3** - Styling moderne avec variables

### Paiements
- **Stripe** - Plateforme de paiement sécurisée
- **Mode Test** - Environnement de développement sûr

### Outils de Développement
- **ESLint** - Qualité du code
- **Vite HMR** - Rechargement à chaud
- **Modern JavaScript** - ES6+ features

## 💳 Système de Paiement

### Cartes de Test Stripe
- **Visa (Succès)** : `4242 4242 4242 4242`
- **Visa (Échec)** : `4000 0000 0000 0002`
- **Mastercard** : `5555 5555 5555 4444`
- **CVV** : `123` | **Date** : `12/34`

### Méthodes Supportées
- 💳 Cartes bancaires (Visa, Mastercard, etc.)
- 🏦 Virements bancaires
- 🔒 Paiements 3D Secure

## 📊 Fonctionnalités Clés

### Interface Étudiant
- **Dashboard personnalisé** avec solde et alertes
- **Échéancier interactif** avec statuts en temps réel
- **Paiement en un clic** sécurisé
- **Historique complet** des transactions
- **Reçus PDF** téléchargeables

### Interface Agent Financier
- **Vue d'ensemble** de tous les étudiants
- **Gestion des paiements** centralisée
- **Rapports avancés** avec filtres
- **Système de relances** automatisé
- **Export de données** multiples formats

### Interface Administrateur
- **Gestion des utilisateurs** complète
- **Configuration système** avancée
- **Monitoring** en temps réel
- **Logs d'audit** détaillés

## 🎯 Objectifs Stratégiques

- 🎯 **Centralisation** des données financières
- ⚡ **Automatisation** des processus répétitifs
- 🔍 **Transparence** totale des transactions
- ⏱️ **Réduction** des délais de paiement
- 🛡️ **Sécurisation** des données sensibles
- 📈 **Amélioration** de l'expérience utilisateur

## 🔒 Sécurité

- 🔐 **Authentification** multi-niveaux
- 🛡️ **Chiffrement** des données sensibles
- 🔑 **Gestion des sessions** sécurisée
- 🚫 **Protection CSRF** intégrée
- 📝 **Audit trail** complet

## 📱 Responsive Design

Optimisé pour tous les appareils :
- 🖥️ **Desktop** (1024px+)
- 📱 **Tablette** (768px - 1023px)
- 📱 **Mobile** (320px - 767px)

## 🚀 Déploiement

### Environnement de Production
```bash
cd frontend
npm run build
npm run preview
```

### Variables d'Environnement
```env
VITE_STRIPE_PUBLIC_KEY=pk_test_...
VITE_API_URL=http://localhost:8000
```

## 📈 Roadmap

### Phase 1 ✅ (Actuelle)
- Interface utilisateur complète
- Authentification et rôles
- Paiements Stripe
- Tableaux de bord

### Phase 2 🔄 (Prochaine)
- Backend Django complet
- Base de données PostgreSQL
- API REST sécurisée
- Intégration Sage

### Phase 3 📋 (Future)
- Notifications push
- Application mobile
- Analytics avancées
- Intégrations bancaires

## 🤝 Contribution

1. **Fork** le projet
2. **Créer** une branche feature (`git checkout -b feature/AmazingFeature`)
3. **Commit** les changements (`git commit -m 'Add AmazingFeature'`)
4. **Push** vers la branche (`git push origin feature/AmazingFeature`)
5. **Ouvrir** une Pull Request

## 📄 Licence

Distribué sous licence MIT. Voir `LICENSE` pour plus d'informations.

## 📞 Support & Contact

### Équipe de Développement
- 📧 **Email** : <EMAIL>
- 💬 **Slack** : #upf-smart-finance
- 🐛 **Issues** : GitHub Issues

### Support Utilisateur
- 📧 **Email** : <EMAIL>
- 📱 **Téléphone** : +689 40 80 38 00
- 🌐 **Site web** : https://www.upf.pf

---

<div align="center">

**🎓 UPF Smart Finance** - *Moderniser la gestion financière universitaire*

Made with ❤️ for Université de Polynésie Française

</div>
