import React, { createContext, useContext, useState, useEffect } from 'react';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  // Mock users for demo purposes
  const mockUsers = [
    {
      id: 1,
      email: '<EMAIL>',
      password: 'password123',
      role: 'student',
      name: '<PERSON>',
      studentId: 'STU001',
      balance: -2500.00,
      payments: [
        {
          id: 1,
          date: '2024-01-15',
          amount: 1500.00,
          description: 'Frais de scolarité - Semestre 1',
          status: 'paid',
          method: 'Carte Bancaire',
          dueDate: '2024-01-10'
        },
        {
          id: 2,
          date: '2024-03-15',
          amount: 1500.00,
          description: 'Frais de scolarité - Semestre 2',
          status: 'unpaid',
          method: null,
          dueDate: '2024-03-10'
        },
        {
          id: 3,
          date: '2024-05-15',
          amount: 1000.00,
          description: 'Frais de logement',
          status: 'unpaid',
          method: null,
          dueDate: '2024-05-10'
        }
      ]
    },
    {
      id: 2,
      email: '<EMAIL>',
      password: 'password123',
      role: 'finance_agent',
      name: 'Marie Martin',
      department: 'Service Financier'
    },
    {
      id: 3,
      email: '<EMAIL>',
      password: 'password123',
      role: 'academic_staff',
      name: 'Pierre Durand',
      department: 'Département Informatique'
    },
    {
      id: 4,
      email: '<EMAIL>',
      password: 'password123',
      role: 'admin',
      name: 'Sophie Leroy',
      department: 'Administration'
    }
  ];

  useEffect(() => {
    // Check for stored authentication
    const storedUser = localStorage.getItem('upf_user');
    if (storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        setUser(userData);
        setIsAuthenticated(true);
      } catch (error) {
        localStorage.removeItem('upf_user');
      }
    }
    setLoading(false);
  }, []);

  const login = async (email, password) => {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const foundUser = mockUsers.find(u => u.email === email && u.password === password);
      
      if (foundUser) {
        const { password: _, ...userWithoutPassword } = foundUser;
        setUser(userWithoutPassword);
        setIsAuthenticated(true);
        localStorage.setItem('upf_user', JSON.stringify(userWithoutPassword));
        return { success: true };
      } else {
        return { success: false, error: 'Email ou mot de passe incorrect' };
      }
    } catch (error) {
      return { success: false, error: 'Erreur de connexion' };
    }
  };

  const register = async (userData) => {
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if user already exists
      const existingUser = mockUsers.find(u => u.email === userData.email);
      if (existingUser) {
        return { success: false, error: 'Un compte avec cet email existe déjà' };
      }

      // Create new user
      const newUser = {
        id: mockUsers.length + 1,
        ...userData,
        balance: 0,
        payments: []
      };

      // In a real app, this would be sent to the backend
      mockUsers.push(newUser);
      
      const { password: _, ...userWithoutPassword } = newUser;
      setUser(userWithoutPassword);
      setIsAuthenticated(true);
      localStorage.setItem('upf_user', JSON.stringify(userWithoutPassword));
      
      return { success: true };
    } catch (error) {
      return { success: false, error: 'Erreur lors de la création du compte' };
    }
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('upf_user');
  };

  const updateUserBalance = (newBalance) => {
    if (user) {
      const updatedUser = { ...user, balance: newBalance };
      setUser(updatedUser);
      localStorage.setItem('upf_user', JSON.stringify(updatedUser));
    }
  };

  const addPayment = (payment) => {
    if (user) {
      const updatedPayments = [...(user.payments || []), payment];
      const updatedUser = { ...user, payments: updatedPayments };
      setUser(updatedUser);
      localStorage.setItem('upf_user', JSON.stringify(updatedUser));
    }
  };

  const updatePaymentStatus = (paymentId, status, method = null) => {
    if (user && user.payments) {
      const updatedPayments = user.payments.map(payment => 
        payment.id === paymentId 
          ? { ...payment, status, method, date: status === 'paid' ? new Date().toISOString().split('T')[0] : payment.date }
          : payment
      );
      const updatedUser = { ...user, payments: updatedPayments };
      setUser(updatedUser);
      localStorage.setItem('upf_user', JSON.stringify(updatedUser));
    }
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    login,
    register,
    logout,
    updateUserBalance,
    addPayment,
    updatePaymentStatus,
    mockUsers
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
