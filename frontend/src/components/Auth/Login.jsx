import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { LogIn, Mail, Lock, Eye, EyeOff } from 'lucide-react';
import './Auth.css';

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { login } = useAuth();
  const navigate = useNavigate();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    const result = await login(formData.email, formData.password);
    
    if (result.success) {
      navigate('/');
    } else {
      setError(result.error);
    }
    
    setLoading(false);
  };

  const demoAccounts = [
    { email: '<EMAIL>', role: 'Étudiant', password: 'password123' },
    { email: '<EMAIL>', role: 'Agent Financier', password: 'password123' },
    { email: '<EMAIL>', role: 'Personnel Académique', password: 'password123' },
    { email: '<EMAIL>', role: 'Administrateur', password: 'password123' }
  ];

  const fillDemoAccount = (email, password) => {
    setFormData({ email, password });
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <div className="logo">
            <h1>UPF Smart Finance</h1>
            <p>Plateforme de Gestion Financière Universitaire</p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="auth-form">
          <h2>
            <LogIn size={24} />
            Connexion
          </h2>

          {error && <div className="error-message">{error}</div>}

          <div className="form-group">
            <label htmlFor="email">
              <Mail size={18} />
              Email
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
              placeholder="<EMAIL>"
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">
              <Lock size={18} />
              Mot de passe
            </label>
            <div className="password-input">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                required
                placeholder="Votre mot de passe"
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
          </div>

          <button type="submit" className="auth-button" disabled={loading}>
            {loading ? 'Connexion...' : 'Se connecter'}
          </button>

          <div className="auth-links">
            <Link to="/register">Créer un compte</Link>
          </div>
        </form>

        <div className="demo-accounts">
          <h3>Comptes de démonstration</h3>
          <div className="demo-grid">
            {demoAccounts.map((account, index) => (
              <div key={index} className="demo-account">
                <div className="demo-info">
                  <strong>{account.role}</strong>
                  <span>{account.email}</span>
                </div>
                <button
                  type="button"
                  onClick={() => fillDemoAccount(account.email, account.password)}
                  className="demo-button"
                >
                  Utiliser
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
