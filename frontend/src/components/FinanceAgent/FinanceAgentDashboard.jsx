import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { 
  Users, 
  CreditCard, 
  BarChart3, 
  Bell,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Euro
} from 'lucide-react';

const FinanceAgentDashboard = () => {
  const { mockUsers } = useAuth();

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  // Get all students
  const students = mockUsers.filter(user => user.role === 'student');
  
  // Calculate statistics
  const totalBalance = students.reduce((sum, student) => sum + (student.balance || 0), 0);
  const overduePayments = students.reduce((count, student) => {
    if (!student.payments) return count;
    const today = new Date();
    return count + student.payments.filter(payment => {
      const dueDate = new Date(payment.dueDate);
      return payment.status === 'unpaid' && dueDate < today;
    }).length;
  }, 0);

  const DashboardHome = () => (
    <div className="finance-dashboard">
      <div className="dashboard-header">
        <h1>Tableau de bord - Agent Financier</h1>
        <p>Gestion des paiements et suivi financier des étudiants</p>
      </div>

      {/* Statistics Cards */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">
            <Users size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">{students.length}</div>
            <div className="stat-label">Étudiants</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon negative">
            <Euro size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">{formatCurrency(Math.abs(totalBalance))}</div>
            <div className="stat-label">Solde total débiteur</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon warning">
            <AlertCircle size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">{overduePayments}</div>
            <div className="stat-label">Paiements en retard</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon positive">
            <TrendingUp size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">85%</div>
            <div className="stat-label">Taux de paiement</div>
          </div>
        </div>
      </div>

      {/* Students List */}
      <div className="students-section">
        <h2>Gestion des étudiants</h2>
        <div className="students-table">
          <table>
            <thead>
              <tr>
                <th>Étudiant</th>
                <th>ID Étudiant</th>
                <th>Solde</th>
                <th>Paiements en retard</th>
                <th>Statut</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {students.map(student => {
                const overdueCount = student.payments?.filter(payment => {
                  const dueDate = new Date(payment.dueDate);
                  return payment.status === 'unpaid' && dueDate < new Date();
                }).length || 0;

                return (
                  <tr key={student.id}>
                    <td>
                      <div className="student-info">
                        <div className="student-name">{student.name}</div>
                        <div className="student-email">{student.email}</div>
                      </div>
                    </td>
                    <td>{student.studentId}</td>
                    <td>
                      <span className={`balance ${student.balance < 0 ? 'negative' : 'positive'}`}>
                        {formatCurrency(student.balance || 0)}
                      </span>
                    </td>
                    <td>
                      {overdueCount > 0 ? (
                        <span className="overdue-count">{overdueCount}</span>
                      ) : (
                        <span className="no-overdue">0</span>
                      )}
                    </td>
                    <td>
                      <span className={`status-badge ${student.balance < 0 ? 'warning' : 'good'}`}>
                        {student.balance < 0 ? 'Débiteur' : 'À jour'}
                      </span>
                    </td>
                    <td>
                      <div className="action-buttons">
                        <button className="btn-small primary">Voir détails</button>
                        {overdueCount > 0 && (
                          <button className="btn-small warning">Relancer</button>
                        )}
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="quick-actions">
        <h2>Actions rapides</h2>
        <div className="actions-grid">
          <div className="action-card">
            <CreditCard size={32} />
            <h3>Nouveau paiement</h3>
            <p>Enregistrer un paiement manuel</p>
            <button className="action-button">Ajouter</button>
          </div>
          <div className="action-card">
            <Bell size={32} />
            <h3>Envoyer relances</h3>
            <p>Relancer les paiements en retard</p>
            <button className="action-button">Envoyer</button>
          </div>
          <div className="action-card">
            <BarChart3 size={32} />
            <h3>Générer rapport</h3>
            <p>Exporter les données financières</p>
            <button className="action-button">Exporter</button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="finance-agent-dashboard">
      <Routes>
        <Route path="/" element={<DashboardHome />} />
        <Route path="/payments" element={<div>Gestion des paiements (à implémenter)</div>} />
        <Route path="/reports" element={<div>Rapports (à implémenter)</div>} />
        <Route path="/reminders" element={<div>Relances (à implémenter)</div>} />
      </Routes>
    </div>
  );
};

export default FinanceAgentDashboard;
