.layout {
  display: flex;
  height: 100vh;
  background: #f5f7fa;
}

/* Sidebar */
.sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e1e5e9;
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: 1000;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar-open {
  transform: translateX(0);
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.sidebar-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-close:hover {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar-user {
  padding: 20px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.user-info {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.user-role {
  color: #666;
  font-size: 12px;
  margin-top: 2px;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #666;
  text-decoration: none;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background: #f8f9fa;
  color: #333;
  border-left-color: #667eea;
}

.nav-item.active {
  background: #f0f4ff;
  color: #667eea;
  border-left-color: #667eea;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid #e1e5e9;
}

.logout-button {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fee;
  color: #c33;
  border: 1px solid #fcc;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-button:hover {
  background: #fdd;
  border-color: #fbb;
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-left: 0;
}

.header {
  background: white;
  border-bottom: 1px solid #e1e5e9;
  padding: 0 20px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.menu-button {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.menu-button:hover {
  background: #f8f9fa;
}

.header-title h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.header-user {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-user .user-info {
  text-align: right;
}

.header-user .user-name {
  display: block;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.header-user .user-role {
  display: block;
  color: #666;
  font-size: 12px;
}

.header-user .user-avatar {
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.page-content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* Desktop Styles */
@media (min-width: 1024px) {
  .sidebar {
    position: static;
    transform: translateX(0);
    box-shadow: none;
  }
  
  .sidebar-close {
    display: none;
  }
  
  .menu-button {
    display: none;
  }
  
  .sidebar-overlay {
    display: none;
  }
  
  .main-content {
    margin-left: 0;
  }
}

/* Tablet Styles */
@media (max-width: 768px) {
  .header-user .user-info {
    display: none;
  }
  
  .page-content {
    padding: 20px;
  }
  
  .header-title h1 {
    font-size: 20px;
  }
}

/* Mobile Styles */
@media (max-width: 480px) {
  .sidebar {
    width: 100%;
  }
  
  .page-content {
    padding: 15px;
  }
  
  .header {
    padding: 0 15px;
  }
}
