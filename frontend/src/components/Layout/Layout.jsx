import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { 
  Menu, 
  X, 
  LogOut, 
  User, 
  Home, 
  CreditCard, 
  FileText, 
  Bell,
  Users,
  Settings,
  BarChart3,
  BookOpen
} from 'lucide-react';
import './Layout.css';

const Layout = ({ children }) => {
  const { user, logout } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleLogout = () => {
    logout();
  };

  const getNavigationItems = () => {
    const baseItems = [
      { icon: Home, label: 'Accueil', path: '/' }
    ];

    switch (user?.role) {
      case 'student':
        return [
          ...baseItems,
          { icon: CreditCard, label: 'Solde', path: '/student' },
          { icon: FileText, label: 'Échéancier', path: '/student/schedule' },
          { icon: FileText, label: 'Reçus de Paiement', path: '/student/receipts' },
          { icon: Bell, label: 'Relances', path: '/student/reminders' }
        ];
      case 'finance_agent':
        return [
          ...baseItems,
          { icon: Users, label: 'Gestion Étudiants', path: '/finance' },
          { icon: CreditCard, label: 'Paiements', path: '/finance/payments' },
          { icon: BarChart3, label: 'Rapports', path: '/finance/reports' },
          { icon: Bell, label: 'Relances', path: '/finance/reminders' }
        ];
      case 'academic_staff':
        return [
          ...baseItems,
          { icon: BookOpen, label: 'Données Étudiants', path: '/academic' },
          { icon: BarChart3, label: 'Rapports', path: '/academic/reports' }
        ];
      case 'admin':
        return [
          ...baseItems,
          { icon: Users, label: 'Gestion Utilisateurs', path: '/admin' },
          { icon: Settings, label: 'Configuration', path: '/admin/settings' },
          { icon: BarChart3, label: 'Logs Système', path: '/admin/logs' },
          { icon: CreditCard, label: 'Intégrations', path: '/admin/integrations' }
        ];
      default:
        return baseItems;
    }
  };

  const navigationItems = getNavigationItems();

  const getRoleDisplayName = (role) => {
    const roleNames = {
      student: 'Étudiant',
      finance_agent: 'Agent Financier',
      academic_staff: 'Personnel Académique',
      admin: 'Administrateur'
    };
    return roleNames[role] || role;
  };

  return (
    <div className="layout">
      {/* Sidebar */}
      <div className={`sidebar ${sidebarOpen ? 'sidebar-open' : ''}`}>
        <div className="sidebar-header">
          <h2>UPF Smart Finance</h2>
          <button 
            className="sidebar-close"
            onClick={() => setSidebarOpen(false)}
          >
            <X size={20} />
          </button>
        </div>

        <div className="sidebar-user">
          <div className="user-avatar">
            <User size={24} />
          </div>
          <div className="user-info">
            <div className="user-name">{user?.name}</div>
            <div className="user-role">{getRoleDisplayName(user?.role)}</div>
          </div>
        </div>

        <nav className="sidebar-nav">
          {navigationItems.map((item, index) => (
            <a
              key={index}
              href={item.path}
              className="nav-item"
              onClick={() => setSidebarOpen(false)}
            >
              <item.icon size={20} />
              <span>{item.label}</span>
            </a>
          ))}
        </nav>

        <div className="sidebar-footer">
          <button className="logout-button" onClick={handleLogout}>
            <LogOut size={20} />
            <span>Déconnexion</span>
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="main-content">
        {/* Header */}
        <header className="header">
          <button 
            className="menu-button"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu size={24} />
          </button>
          
          <div className="header-title">
            <h1>UPF Smart Finance</h1>
          </div>

          <div className="header-user">
            <div className="user-info">
              <span className="user-name">{user?.name}</span>
              <span className="user-role">{getRoleDisplayName(user?.role)}</span>
            </div>
            <div className="user-avatar">
              <User size={20} />
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="page-content">
          {children}
        </main>
      </div>

      {/* Sidebar Overlay */}
      {sidebarOpen && (
        <div 
          className="sidebar-overlay"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default Layout;
