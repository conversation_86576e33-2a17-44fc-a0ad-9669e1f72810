.student-dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

/* Dashboard Home */
.dashboard-home {
  display: grid;
  gap: 30px;
}

.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.balance-header h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
}

.balance-amount {
  margin-bottom: 15px;
}

.amount {
  font-size: 48px;
  font-weight: 700;
  display: block;
}

.amount.negative {
  color: #ff6b6b;
}

.amount.positive {
  color: #51cf66;
}

.balance-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 107, 107, 0.2);
  padding: 12px;
  border-radius: 8px;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

/* Quick Actions */
.quick-actions h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.action-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  color: #333;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.action-card svg {
  color: #667eea;
}

.action-card span {
  font-weight: 500;
  text-align: center;
}

/* Alert Cards */
.alert-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid;
}

.alert-card.danger {
  background: #fee;
  border-left-color: #ff6b6b;
  color: #c92a2a;
}

.alert-card.warning {
  background: #fff4e6;
  border-left-color: #fd7e14;
  color: #d9480f;
}

.alert-card strong {
  display: block;
  margin-bottom: 4px;
}

.alert-card p {
  margin: 0;
  font-size: 14px;
}

/* Recent Payments */
.recent-payments h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.payments-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.payment-item {
  display: grid;
  grid-template-columns: 1fr auto auto auto;
  align-items: center;
  gap: 20px;
  padding: 20px;
  border-bottom: 1px solid #f1f3f4;
}

.payment-item:last-child {
  border-bottom: none;
}

.payment-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.payment-description {
  font-weight: 500;
  color: #333;
}

.payment-date {
  font-size: 14px;
  color: #666;
}

.payment-amount {
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.payment-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}

.status-icon.paid {
  color: #51cf66;
}

.status-icon.unpaid {
  color: #ff6b6b;
}

.status-icon.overdue {
  color: #fd7e14;
}

.pay-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s ease;
}

.pay-button:hover {
  background: #5a6fd8;
}

.pay-button.small {
  padding: 6px 12px;
  font-size: 14px;
}

/* Payment Schedule */
.payment-schedule {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.page-header {
  padding: 30px;
  border-bottom: 1px solid #f1f3f4;
}

.page-header h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.payments-table {
  overflow-x: auto;
}

.payments-table table {
  width: 100%;
  border-collapse: collapse;
}

.payments-table th {
  background: #f8f9fa;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 1px solid #e9ecef;
}

.payments-table td {
  padding: 16px;
  border-bottom: 1px solid #f1f3f4;
}

.payments-table tr:hover {
  background: #f8f9fa;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.paid {
  background: #d3f9d8;
  color: #2b8a3e;
}

.status-badge.unpaid {
  background: #ffe0e6;
  color: #c92a2a;
}

.status-badge.overdue {
  background: #fff4e6;
  color: #d9480f;
}

.download-button {
  background: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: background 0.2s ease;
}

.download-button:hover {
  background: #5a6268;
}

/* Payment Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.payment-modal {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #f1f3f4;
}

.modal-header h2 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-button:hover {
  background: #f8f9fa;
}

.modal-content {
  padding: 24px;
}

.payment-details,
.payment-methods,
.payment-form {
  margin-bottom: 24px;
}

.payment-details h3,
.payment-methods h3,
.payment-form h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f4;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row .amount {
  font-weight: 600;
  color: #667eea;
}

.method-options {
  display: grid;
  gap: 12px;
}

.method-option {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.method-option.selected {
  border-color: #667eea;
  background: #f0f4ff;
}

.method-option input {
  display: none;
}

.method-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  color: #333;
  font-weight: 500;
  font-size: 14px;
}

.card-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.card-input:focus {
  outline: none;
  border-color: #667eea;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.transfer-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.info-box {
  background: white;
  padding: 16px;
  border-radius: 6px;
  margin: 16px 0;
  border: 1px solid #e9ecef;
}

.info-box p {
  margin: 8px 0;
  font-size: 14px;
}

.transfer-note {
  font-size: 14px;
  color: #666;
  margin: 16px 0 0 0;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #fee;
  color: #c92a2a;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #fcc;
  margin-top: 16px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #f1f3f4;
}

.cancel-button {
  padding: 12px 24px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s ease;
}

.cancel-button:hover {
  background: #5a6268;
}

.pay-button {
  padding: 12px 24px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background 0.2s ease;
}

.pay-button:hover:not(:disabled) {
  background: #5a6fd8;
}

.pay-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Success Modal */
.success-modal {
  text-align: center;
  padding: 40px;
}

.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.success-icon {
  color: #51cf66;
}

.success-content h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.success-content p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

/* Responsive */
@media (max-width: 768px) {
  .payment-item {
    grid-template-columns: 1fr;
    gap: 12px;
    text-align: center;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .modal-footer {
    flex-direction: column;
  }
  
  .payments-table {
    font-size: 14px;
  }
  
  .payments-table th,
  .payments-table td {
    padding: 12px 8px;
  }
}
