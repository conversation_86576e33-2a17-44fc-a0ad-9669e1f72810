import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { 
  X, 
  CreditCard, 
  Building2, 
  CheckCircle, 
  AlertCircle,
  Loader
} from 'lucide-react';

const PaymentModal = ({ payment, onClose }) => {
  const { updatePaymentStatus, updateUserBalance, user } = useAuth();
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [processing, setProcessing] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const handlePayment = async () => {
    setProcessing(true);
    setError('');

    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate random success/failure for demo
      const isSuccess = Math.random() > 0.1; // 90% success rate

      if (isSuccess) {
        // Update payment status
        updatePaymentStatus(payment.id, 'paid', paymentMethod === 'card' ? 'Carte Bancaire' : 'Virement');
        
        // Update user balance
        const newBalance = user.balance + payment.amount;
        updateUserBalance(newBalance);
        
        setSuccess(true);
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        setError('Le paiement a échoué. Veuillez réessayer.');
      }
    } catch (err) {
      setError('Une erreur est survenue lors du paiement.');
    } finally {
      setProcessing(false);
    }
  };

  if (success) {
    return (
      <div className="modal-overlay">
        <div className="payment-modal success-modal">
          <div className="success-content">
            <CheckCircle size={64} className="success-icon" />
            <h2>Paiement réussi !</h2>
            <p>Votre paiement de {formatCurrency(payment.amount)} a été traité avec succès.</p>
            <p>Un reçu vous sera envoyé par email.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="modal-overlay">
      <div className="payment-modal">
        <div className="modal-header">
          <h2>Effectuer un paiement</h2>
          <button className="close-button" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="modal-content">
          {/* Payment Details */}
          <div className="payment-details">
            <h3>Détails du paiement</h3>
            <div className="detail-row">
              <span>Description:</span>
              <span>{payment.description}</span>
            </div>
            <div className="detail-row">
              <span>Montant:</span>
              <span className="amount">{formatCurrency(payment.amount)}</span>
            </div>
            <div className="detail-row">
              <span>Date d'échéance:</span>
              <span>{new Date(payment.dueDate).toLocaleDateString('fr-FR')}</span>
            </div>
          </div>

          {/* Payment Method Selection */}
          <div className="payment-methods">
            <h3>Méthode de paiement</h3>
            <div className="method-options">
              <label className={`method-option ${paymentMethod === 'card' ? 'selected' : ''}`}>
                <input
                  type="radio"
                  value="card"
                  checked={paymentMethod === 'card'}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                />
                <div className="method-content">
                  <CreditCard size={24} />
                  <span>Carte Bancaire</span>
                </div>
              </label>

              <label className={`method-option ${paymentMethod === 'transfer' ? 'selected' : ''}`}>
                <input
                  type="radio"
                  value="transfer"
                  checked={paymentMethod === 'transfer'}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                />
                <div className="method-content">
                  <Building2 size={24} />
                  <span>Virement Bancaire</span>
                </div>
              </label>
            </div>
          </div>

          {/* Payment Form */}
          {paymentMethod === 'card' && (
            <div className="payment-form">
              <h3>Informations de carte</h3>
              <div className="form-group">
                <label>Numéro de carte</label>
                <input
                  type="text"
                  placeholder="1234 5678 9012 3456"
                  className="card-input"
                />
              </div>
              <div className="form-row">
                <div className="form-group">
                  <label>Date d'expiration</label>
                  <input
                    type="text"
                    placeholder="MM/AA"
                    className="card-input"
                  />
                </div>
                <div className="form-group">
                  <label>CVV</label>
                  <input
                    type="text"
                    placeholder="123"
                    className="card-input"
                  />
                </div>
              </div>
              <div className="form-group">
                <label>Nom sur la carte</label>
                <input
                  type="text"
                  placeholder="Jean Dupont"
                  className="card-input"
                />
              </div>
            </div>
          )}

          {paymentMethod === 'transfer' && (
            <div className="transfer-info">
              <h3>Informations de virement</h3>
              <div className="info-box">
                <p><strong>Bénéficiaire:</strong> Université de Polynésie Française</p>
                <p><strong>IBAN:</strong> FR76 1234 5678 9012 3456 7890 123</p>
                <p><strong>BIC:</strong> BNPAFRPPXXX</p>
                <p><strong>Référence:</strong> {user?.studentId}-{payment.id}</p>
              </div>
              <p className="transfer-note">
                Veuillez inclure la référence dans le libellé de votre virement.
                Le traitement peut prendre 1-3 jours ouvrés.
              </p>
            </div>
          )}

          {error && (
            <div className="error-message">
              <AlertCircle size={16} />
              {error}
            </div>
          )}
        </div>

        <div className="modal-footer">
          <button className="cancel-button" onClick={onClose}>
            Annuler
          </button>
          <button 
            className="pay-button"
            onClick={handlePayment}
            disabled={processing}
          >
            {processing ? (
              <>
                <Loader size={16} className="spinner" />
                Traitement...
              </>
            ) : (
              <>
                Payer {formatCurrency(payment.amount)}
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentModal;
