import React, { useState } from 'react';
import { Routes, Route, Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { 
  CreditCard, 
  Calendar, 
  FileText, 
  Bell, 
  Euro, 
  AlertCircle,
  CheckCircle,
  Clock,
  Download
} from 'lucide-react';
import PaymentModal from './PaymentModal';
import './Student.css';

const StudentDashboard = () => {
  const { user } = useAuth();
  const location = useLocation();
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('fr-FR');
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="status-icon paid" size={16} />;
      case 'unpaid':
        return <AlertCircle className="status-icon unpaid" size={16} />;
      case 'overdue':
        return <Clock className="status-icon overdue" size={16} />;
      default:
        return <Clock className="status-icon" size={16} />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'paid':
        return 'Payé';
      case 'unpaid':
        return 'Non payé';
      case 'overdue':
        return 'En retard';
      default:
        return status;
    }
  };

  const handlePayment = (payment) => {
    setSelectedPayment(payment);
    setShowPaymentModal(true);
  };

  const getOverduePayments = () => {
    if (!user?.payments) return [];
    const today = new Date();
    return user.payments.filter(payment => {
      const dueDate = new Date(payment.dueDate);
      return payment.status === 'unpaid' && dueDate < today;
    });
  };

  const getUpcomingPayments = () => {
    if (!user?.payments) return [];
    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
    return user.payments.filter(payment => {
      const dueDate = new Date(payment.dueDate);
      return payment.status === 'unpaid' && dueDate >= today && dueDate <= nextMonth;
    });
  };

  const overduePayments = getOverduePayments();
  const upcomingPayments = getUpcomingPayments();

  // Main Dashboard View
  const DashboardHome = () => (
    <div className="dashboard-home">
      {/* Balance Card */}
      <div className="balance-card">
        <div className="balance-header">
          <h2>
            <CreditCard size={24} />
            Solde du compte
          </h2>
        </div>
        <div className="balance-amount">
          <span className={`amount ${user?.balance < 0 ? 'negative' : 'positive'}`}>
            {formatCurrency(user?.balance || 0)}
          </span>
        </div>
        {user?.balance < 0 && (
          <div className="balance-warning">
            <AlertCircle size={16} />
            Solde débiteur - Paiement requis
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="quick-actions">
        <h3>Actions rapides</h3>
        <div className="actions-grid">
          <Link to="/student/schedule" className="action-card">
            <Calendar size={24} />
            <span>Voir l'échéancier</span>
          </Link>
          <Link to="/student/receipts" className="action-card">
            <FileText size={24} />
            <span>Reçus de paiement</span>
          </Link>
          <Link to="/student/reminders" className="action-card">
            <Bell size={24} />
            <span>Relances</span>
          </Link>
        </div>
      </div>

      {/* Alerts */}
      {overduePayments.length > 0 && (
        <div className="alert-card danger">
          <AlertCircle size={20} />
          <div>
            <strong>Paiements en retard</strong>
            <p>Vous avez {overduePayments.length} paiement(s) en retard</p>
          </div>
        </div>
      )}

      {upcomingPayments.length > 0 && (
        <div className="alert-card warning">
          <Clock size={20} />
          <div>
            <strong>Paiements à venir</strong>
            <p>{upcomingPayments.length} paiement(s) dû(s) dans le mois</p>
          </div>
        </div>
      )}

      {/* Recent Payments */}
      <div className="recent-payments">
        <h3>Derniers paiements</h3>
        <div className="payments-list">
          {user?.payments?.slice(0, 3).map(payment => (
            <div key={payment.id} className="payment-item">
              <div className="payment-info">
                <div className="payment-description">{payment.description}</div>
                <div className="payment-date">Échéance: {formatDate(payment.dueDate)}</div>
              </div>
              <div className="payment-amount">{formatCurrency(payment.amount)}</div>
              <div className="payment-status">
                {getStatusIcon(payment.status)}
                {getStatusText(payment.status)}
              </div>
              {payment.status === 'unpaid' && (
                <button 
                  className="pay-button"
                  onClick={() => handlePayment(payment)}
                >
                  Payer
                </button>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Payment Schedule View
  const PaymentSchedule = () => (
    <div className="payment-schedule">
      <div className="page-header">
        <h2>
          <Calendar size={24} />
          Échéancier des paiements
        </h2>
      </div>

      <div className="payments-table">
        <table>
          <thead>
            <tr>
              <th>Date d'échéance</th>
              <th>Description</th>
              <th>Montant</th>
              <th>Statut</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {user?.payments?.map(payment => (
              <tr key={payment.id} className={payment.status}>
                <td>{formatDate(payment.dueDate)}</td>
                <td>{payment.description}</td>
                <td>{formatCurrency(payment.amount)}</td>
                <td>
                  <span className={`status-badge ${payment.status}`}>
                    {getStatusIcon(payment.status)}
                    {getStatusText(payment.status)}
                  </span>
                </td>
                <td>
                  {payment.status === 'unpaid' ? (
                    <button 
                      className="pay-button small"
                      onClick={() => handlePayment(payment)}
                    >
                      Payer
                    </button>
                  ) : (
                    <button className="download-button small">
                      <Download size={14} />
                      Reçu
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  return (
    <div className="student-dashboard">
      <Routes>
        <Route path="/" element={<DashboardHome />} />
        <Route path="/schedule" element={<PaymentSchedule />} />
        <Route path="/receipts" element={<div>Reçus de paiement (à implémenter)</div>} />
        <Route path="/reminders" element={<div>Relances (à implémenter)</div>} />
      </Routes>

      {showPaymentModal && (
        <PaymentModal
          payment={selectedPayment}
          onClose={() => setShowPaymentModal(false)}
        />
      )}
    </div>
  );
};

export default StudentDashboard;
