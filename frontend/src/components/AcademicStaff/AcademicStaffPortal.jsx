import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { BookOpen, Users, BarChart3, FileText } from 'lucide-react';

const AcademicStaffPortal = () => {
  const { mockUsers } = useAuth();

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  // Get all students
  const students = mockUsers.filter(user => user.role === 'student');

  return (
    <div className="academic-staff-portal">
      <div className="dashboard-header">
        <h1>Portail Personnel Académique</h1>
        <p>Consultation des données financières des étudiants (lecture seule)</p>
      </div>

      {/* Overview Cards */}
      <div className="overview-grid">
        <div className="overview-card">
          <div className="card-icon">
            <Users size={32} />
          </div>
          <div className="card-content">
            <h3>Étudiants inscrits</h3>
            <div className="card-value">{students.length}</div>
          </div>
        </div>

        <div className="overview-card">
          <div className="card-icon">
            <FileText size={32} />
          </div>
          <div className="card-content">
            <h3>Paiements en cours</h3>
            <div className="card-value">
              {students.reduce((count, student) => {
                return count + (student.payments?.filter(p => p.status === 'unpaid').length || 0);
              }, 0)}
            </div>
          </div>
        </div>

        <div className="overview-card">
          <div className="card-icon">
            <BarChart3 size={32} />
          </div>
          <div className="card-content">
            <h3>Taux de paiement</h3>
            <div className="card-value">85%</div>
          </div>
        </div>
      </div>

      {/* Students Financial Status */}
      <div className="students-financial-status">
        <h2>Statut financier des étudiants</h2>
        <div className="status-table">
          <table>
            <thead>
              <tr>
                <th>Étudiant</th>
                <th>ID Étudiant</th>
                <th>Solde actuel</th>
                <th>Paiements en attente</th>
                <th>Statut</th>
              </tr>
            </thead>
            <tbody>
              {students.map(student => {
                const unpaidPayments = student.payments?.filter(p => p.status === 'unpaid').length || 0;
                const isUpToDate = student.balance >= 0 && unpaidPayments === 0;

                return (
                  <tr key={student.id}>
                    <td>
                      <div className="student-info">
                        <div className="student-name">{student.name}</div>
                        <div className="student-email">{student.email}</div>
                      </div>
                    </td>
                    <td>{student.studentId}</td>
                    <td>
                      <span className={`balance ${student.balance < 0 ? 'negative' : 'positive'}`}>
                        {formatCurrency(student.balance || 0)}
                      </span>
                    </td>
                    <td>
                      <span className="pending-count">{unpaidPayments}</span>
                    </td>
                    <td>
                      <span className={`status-indicator ${isUpToDate ? 'good' : 'warning'}`}>
                        {isUpToDate ? 'À jour' : 'En attente'}
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Information Notice */}
      <div className="info-notice">
        <BookOpen size={24} />
        <div>
          <h3>Accès en lecture seule</h3>
          <p>
            En tant que personnel académique, vous avez accès aux informations financières 
            des étudiants en lecture seule. Pour toute modification ou action sur les comptes, 
            veuillez contacter le service financier.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AcademicStaffPortal;
