import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { 
  Users, 
  Settings, 
  BarChart3, 
  CreditCard,
  Shield,
  Activity,
  Plus,
  Edit,
  Trash2
} from 'lucide-react';

const AdminPanel = () => {
  const { mockUsers } = useAuth();
  const [activeTab, setActiveTab] = useState('users');

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const getRoleDisplayName = (role) => {
    const roleNames = {
      student: 'Étudiant',
      finance_agent: 'Agent Financier',
      academic_staff: 'Personnel Académique',
      admin: 'Administrateur'
    };
    return roleNames[role] || role;
  };

  const getRoleColor = (role) => {
    const colors = {
      student: 'blue',
      finance_agent: 'green',
      academic_staff: 'orange',
      admin: 'red'
    };
    return colors[role] || 'gray';
  };

  const UserManagement = () => (
    <div className="user-management">
      <div className="section-header">
        <h2>Gestion des utilisateurs</h2>
        <button className="btn-primary">
          <Plus size={16} />
          Nouvel utilisateur
        </button>
      </div>

      <div className="users-table">
        <table>
          <thead>
            <tr>
              <th>Utilisateur</th>
              <th>Rôle</th>
              <th>Statut</th>
              <th>Dernière connexion</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {mockUsers.map(user => (
              <tr key={user.id}>
                <td>
                  <div className="user-info">
                    <div className="user-name">{user.name}</div>
                    <div className="user-email">{user.email}</div>
                  </div>
                </td>
                <td>
                  <span className={`role-badge ${getRoleColor(user.role)}`}>
                    {getRoleDisplayName(user.role)}
                  </span>
                </td>
                <td>
                  <span className="status-badge active">Actif</span>
                </td>
                <td>Il y a 2 heures</td>
                <td>
                  <div className="action-buttons">
                    <button className="btn-icon" title="Modifier">
                      <Edit size={14} />
                    </button>
                    <button className="btn-icon danger" title="Supprimer">
                      <Trash2 size={14} />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const SystemSettings = () => (
    <div className="system-settings">
      <h2>Configuration système</h2>
      
      <div className="settings-grid">
        <div className="setting-card">
          <h3>Paramètres de paiement</h3>
          <div className="setting-item">
            <label>Délai de grâce (jours)</label>
            <input type="number" defaultValue="7" />
          </div>
          <div className="setting-item">
            <label>Frais de retard (%)</label>
            <input type="number" defaultValue="2" />
          </div>
          <button className="btn-primary">Sauvegarder</button>
        </div>

        <div className="setting-card">
          <h3>Notifications</h3>
          <div className="setting-item">
            <label>
              <input type="checkbox" defaultChecked />
              Rappels automatiques
            </label>
          </div>
          <div className="setting-item">
            <label>
              <input type="checkbox" defaultChecked />
              Notifications par email
            </label>
          </div>
          <button className="btn-primary">Sauvegarder</button>
        </div>

        <div className="setting-card">
          <h3>Intégrations</h3>
          <div className="setting-item">
            <label>Stripe (Test Mode)</label>
            <span className="status-indicator connected">Connecté</span>
          </div>
          <div className="setting-item">
            <label>Sage Comptabilité</label>
            <span className="status-indicator disconnected">Déconnecté</span>
          </div>
          <button className="btn-primary">Configurer</button>
        </div>
      </div>
    </div>
  );

  const SystemLogs = () => (
    <div className="system-logs">
      <h2>Logs système</h2>
      
      <div className="logs-table">
        <table>
          <thead>
            <tr>
              <th>Timestamp</th>
              <th>Utilisateur</th>
              <th>Action</th>
              <th>Détails</th>
              <th>Statut</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>2024-01-15 14:30:25</td>
              <td><EMAIL></td>
              <td>Paiement</td>
              <td>Paiement de 1500€ par carte bancaire</td>
              <td><span className="status-badge success">Succès</span></td>
            </tr>
            <tr>
              <td>2024-01-15 14:25:12</td>
              <td><EMAIL></td>
              <td>Connexion</td>
              <td>Connexion au tableau de bord</td>
              <td><span className="status-badge success">Succès</span></td>
            </tr>
            <tr>
              <td>2024-01-15 14:20:08</td>
              <td><EMAIL></td>
              <td>Consultation</td>
              <td>Consultation des données étudiants</td>
              <td><span className="status-badge success">Succès</span></td>
            </tr>
            <tr>
              <td>2024-01-15 14:15:33</td>
              <td><EMAIL></td>
              <td>Connexion</td>
              <td>Tentative de connexion échouée</td>
              <td><span className="status-badge error">Échec</span></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );

  const tabs = [
    { id: 'users', label: 'Utilisateurs', icon: Users, component: UserManagement },
    { id: 'settings', label: 'Configuration', icon: Settings, component: SystemSettings },
    { id: 'logs', label: 'Logs', icon: Activity, component: SystemLogs }
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component || UserManagement;

  return (
    <div className="admin-panel">
      <div className="dashboard-header">
        <h1>Panneau d'administration</h1>
        <p>Gestion complète du système UPF Smart Finance</p>
      </div>

      {/* Statistics Overview */}
      <div className="admin-stats">
        <div className="stat-card">
          <div className="stat-icon">
            <Users size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">{mockUsers.length}</div>
            <div className="stat-label">Utilisateurs totaux</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <Shield size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">{mockUsers.filter(u => u.role === 'admin').length}</div>
            <div className="stat-label">Administrateurs</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <Activity size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">99.9%</div>
            <div className="stat-label">Disponibilité</div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            <CreditCard size={24} />
          </div>
          <div className="stat-content">
            <div className="stat-value">156</div>
            <div className="stat-label">Transactions aujourd'hui</div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="tab-navigation">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <tab.icon size={18} />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        <ActiveComponent />
      </div>
    </div>
  );
};

export default AdminPanel;
