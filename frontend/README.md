# UPF Smart Finance - Frontend

Une plateforme moderne de gestion financière universitaire développée avec React.js et Vite.

## 🎯 Vue d'ensemble

UPF Smart Finance est une application web complète conçue pour la gestion financière d'une université. Elle offre des interfaces spécialisées pour différents types d'utilisateurs avec des fonctionnalités adaptées à leurs besoins.

## 🔐 Rôles utilisateur

### Étudiant
- Consultation du solde du compte
- Visualisation de l'échéancier des paiements
- Paiement en ligne (Carte bancaire / Virement)
- Téléchargement des reçus de paiement
- Gestion des relances

### Agent Financier
- Gestion des comptes étudiants
- Suivi des paiements
- Génération de rapports
- Envoi de relances automatiques

### Personnel Académique
- Consultation en lecture seule des données financières
- Accès aux rapports de paiement des étudiants

### Administrateur
- Gestion complète des utilisateurs
- Configuration du système
- Consultation des logs d'activité
- Gestion des intégrations (Stripe, Sage)

## 🚀 Démarrage rapide

### Prérequis
- Node.js (version 16 ou supérieure)
- npm ou yarn

### Installation

1. **Cloner le projet**
   ```bash
   git clone <repository-url>
   cd UPF/frontend
   ```

2. **Installer les dépendances**
   ```bash
   npm install
   ```

3. **Lancer le serveur de développement**
   ```bash
   npm run dev
   ```

4. **Ouvrir l'application**
   Naviguer vers `http://localhost:5173`

## 🧪 Comptes de démonstration

L'application inclut des comptes de test prêts à utiliser :

| Rôle | Email | Mot de passe |
|------|-------|--------------|
| Étudiant | <EMAIL> | password123 |
| Agent Financier | <EMAIL> | password123 |
| Personnel Académique | <EMAIL> | password123 |
| Administrateur | <EMAIL> | password123 |

## 🛠️ Technologies utilisées

### Frontend
- **React 18** - Bibliothèque UI
- **Vite** - Outil de build moderne
- **React Router** - Navigation côté client
- **Lucide React** - Icônes modernes
- **Date-fns** - Manipulation des dates

### Paiements
- **Stripe** - Intégration de paiement (mode test)
- Support des cartes bancaires et virements

### Styling
- **CSS3** avec variables CSS personnalisées
- Design responsive
- Thème moderne avec dégradés

## 📁 Structure du projet

```
frontend/
├── public/                 # Fichiers statiques
├── src/
│   ├── components/        # Composants React
│   │   ├── Auth/         # Authentification
│   │   ├── Student/      # Interface étudiant
│   │   ├── FinanceAgent/ # Interface agent financier
│   │   ├── AcademicStaff/# Interface personnel académique
│   │   ├── Admin/        # Interface administrateur
│   │   └── Layout/       # Composants de mise en page
│   ├── contexts/         # Contextes React
│   ├── App.jsx          # Composant principal
│   ├── main.jsx         # Point d'entrée
│   └── index.css        # Styles globaux
├── package.json
└── vite.config.js
```

## 🎨 Fonctionnalités principales

### Interface Étudiant
- **Tableau de bord** avec solde et alertes
- **Échéancier** des paiements avec statuts
- **Paiement en ligne** sécurisé via Stripe
- **Historique** des transactions
- **Téléchargement** des reçus PDF

### Interface Agent Financier
- **Gestion des étudiants** avec vue d'ensemble
- **Suivi des paiements** en temps réel
- **Génération de rapports** exportables
- **Système de relances** automatisé

### Interface Administrateur
- **Gestion des utilisateurs** complète
- **Configuration système** avancée
- **Logs d'activité** détaillés
- **Intégrations** tierces (Stripe, Sage)

## 💳 Intégration Stripe

L'application utilise Stripe en mode test pour les paiements :

### Cartes de test
- **Visa réussie** : 4242 4242 4242 4242
- **Visa échouée** : 4000 0000 0000 0002
- **Mastercard** : 5555 5555 5555 4444

### Configuration
- Mode test activé par défaut
- Webhooks configurés pour les confirmations
- Support des paiements 3D Secure

## 🔧 Scripts disponibles

```bash
# Développement
npm run dev          # Lance le serveur de développement

# Production
npm run build        # Compile l'application
npm run preview      # Prévisualise la version de production

# Qualité du code
npm run lint         # Vérifie le code avec ESLint
```

## 🌐 Déploiement

### Build de production
```bash
npm run build
```

### Serveur statique
```bash
npm run preview
```

Les fichiers compilés se trouvent dans le dossier `dist/`.

## 🔒 Sécurité

- **Authentification** basée sur les rôles
- **Validation** côté client et serveur
- **Chiffrement** des données sensibles
- **Sessions** sécurisées avec localStorage

## 📱 Responsive Design

L'application est entièrement responsive et optimisée pour :
- **Desktop** (1024px+)
- **Tablette** (768px - 1023px)
- **Mobile** (320px - 767px)

## 🎯 Objectifs stratégiques

- ✅ Centraliser les données financières
- ✅ Automatiser les tâches répétitives
- ✅ Améliorer la transparence
- ✅ Réduire les délais de paiement
- ✅ Minimiser les litiges

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 📞 Support

Pour toute question ou problème :
- 📧 Email : <EMAIL>
- 📱 Téléphone : +689 40 80 38 00
- 🌐 Site web : https://www.upf.pf

---

**UPF Smart Finance** - Moderniser la gestion financière universitaire 🎓💰
